import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  LayoutDashboard,
  User,
  Settings,
  ChevronLeft,
  ChevronRight,
  X,
  Palette
} from 'lucide-react';
import { useAuth } from '../contexts/AppContext';
import { useFocusTrap, useKeyboardNavigation, ariaUtils } from '../utils/accessibility';
import { useIsTouchDevice } from '../utils/responsive';
import { hapticFeedback, useSwipeGesture, touchFriendlyProps } from '../utils/touch';

interface SidebarProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

interface NavItem {
  label: string;
  path: string;
  icon: React.ComponentType<any>;
  requireAuth?: boolean;
}

const navItems: NavItem[] = [
  {
    label: 'Home',
    path: '/',
    icon: Home,
    requireAuth: false
  },
  {
    label: 'Dashboard',
    path: '/dashboard',
    icon: LayoutDashboard,
    requireAuth: true
  },
  {
    label: 'Profile',
    path: '/profile',
    icon: User,
    requireAuth: true
  },
  {
    label: 'Settings',
    path: '/settings',
    icon: Settings,
    requireAuth: true
  },
  {
    label: 'Showcase',
    path: '/showcase',
    icon: Palette,
    requireAuth: false
  }
];

const Sidebar: React.FC<SidebarProps> = ({ isOpen, setIsOpen }) => {
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const [isCollapsed, setIsCollapsed] = useState(true); // Start collapsed by default
  const [isHovered, setIsHovered] = useState(false);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTouch = useIsTouchDevice();

  // Focus trap for mobile sidebar
  const sidebarRef = useFocusTrap(isOpen) as React.RefObject<HTMLDivElement>;

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
    if (isTouch) hapticFeedback.light();
  };

  const closeSidebar = () => {
    setIsOpen(false);
    if (isTouch) hapticFeedback.medium();
  };

  // Swipe gesture to close sidebar on mobile
  const swipeHandlers = useSwipeGesture({
    onSwipeLeft: () => {
      if (isOpen && window.innerWidth < 1024) {
        closeSidebar();
      }
    },
  });

  // Convert touch handlers for React events
  const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    swipeHandlers.onTouchStart(e.nativeEvent);
  };

  const handleTouchEnd = (e: React.TouchEvent<HTMLDivElement>) => {
    swipeHandlers.onTouchEnd(e.nativeEvent);
  };

  const handleMouseEnter = () => {
    // Clear any existing timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    // Set a timeout to collapse after a brief delay
    hoverTimeoutRef.current = setTimeout(() => {
      setIsHovered(false);
    }, 300); // 300ms delay before auto-collapse
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  // Determine if sidebar should be expanded
  const shouldExpand = isHovered || !isCollapsed;

  const filteredNavItems = navItems.filter(item =>
    !item.requireAuth || isAuthenticated
  );

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        ref={sidebarRef}
        className={`
          fixed left-0 top-0 h-full bg-white dark:bg-gray-900 shadow-lg dark:shadow-gray-800 z-50 transition-all duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          ${shouldExpand ? 'w-64' : 'w-16'}
          lg:relative lg:z-auto
        `}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        role="navigation"
        aria-label="Main navigation"
        id="sidebar"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          {shouldExpand && (
            <h2 className="text-xl font-bold text-gray-800 dark:text-white">
              AI Agent
            </h2>
          )}

          {/* Desktop collapse toggle */}
          <button
            onClick={toggleCollapse}
            className={`
              hidden lg:flex items-center justify-center w-8 h-8 rounded-full
              hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors
              focus:outline-none focus:ring-2 focus:ring-blue-500
              ${touchFriendlyProps.touchFeedback}
            `}
            style={touchFriendlyProps.minTouchTarget}
            aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            aria-expanded={!isCollapsed}
          >
            {isCollapsed ? (
              <ChevronRight className="w-4 h-4 text-gray-600 dark:text-gray-400" aria-hidden="true" />
            ) : (
              <ChevronLeft className="w-4 h-4 text-gray-600 dark:text-gray-400" aria-hidden="true" />
            )}
          </button>

          {/* Mobile close button */}
          <button
            onClick={closeSidebar}
            className={`
              lg:hidden flex items-center justify-center w-8 h-8 rounded-full
              hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors
              focus:outline-none focus:ring-2 focus:ring-blue-500
              ${touchFriendlyProps.touchFeedback}
            `}
            style={touchFriendlyProps.minTouchTarget}
            aria-label="Close sidebar"
          >
            <X className="w-4 h-4 text-gray-600 dark:text-gray-400" aria-hidden="true" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4" role="menu" aria-label="Main navigation menu">
          <ul className="space-y-2" role="none">
            {filteredNavItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;

              return (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    onClick={() => {
                      // Close mobile sidebar on navigation
                      if (window.innerWidth < 1024) {
                        closeSidebar();
                      }
                      if (isTouch) hapticFeedback.light();
                    }}
                    className={`
                      flex items-center px-3 py-2 rounded-lg transition-all duration-200
                      ${touchFriendlyProps.touchFeedback}
                      ${isActive
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border-r-2 border-blue-700 dark:border-blue-400'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                      }
                      ${!shouldExpand ? 'justify-center' : 'justify-start'}
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                    `}
                    style={touchFriendlyProps.minTouchTarget}
                    title={!shouldExpand ? item.label : ''}
                    aria-label={item.label}
                    aria-current={isActive ? 'page' : undefined}
                    role="menuitem"
                  >
                    <Icon
                      className={`w-5 h-5 ${!shouldExpand ? '' : 'mr-3'}`}
                      aria-hidden="true"
                    />
                    {shouldExpand && (
                      <span className="font-medium">{item.label}</span>
                    )}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>
      </div>
    </>
  );
};

export default Sidebar;
