import React, { useState, Suspense, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import './App.css';
import { AppProvider } from './contexts/AppContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { PreferencesProvider } from './contexts/PreferencesContext';
import { queryClient } from './lib/queryClient';
import Navbar from './components/Navbar';
import MobileNavbar from './components/MobileNavbar';
import Sidebar from './components/Sidebar';
import BottomNavigation from './components/BottomNavigation';
import SkipNavigation from './components/SkipNavigation';
import Breadcrumb from './components/Breadcrumb';
import ToastContainer from './components/ToastContainer';
import PageLoading from './components/PageLoading';
import ErrorBoundary from './components/ErrorBoundary';
import { initializeSecurity } from './utils/security';
import { useIsMobile } from './utils/responsive';

// Lazy load page components
const HomePage = React.lazy(() => import('./pages/HomePage'));
const LoginPage = React.lazy(() => import('./pages/LoginPage'));
const RegisterPage = React.lazy(() => import('./pages/RegisterPage'));
const DashboardPage = React.lazy(() => import('./pages/DashboardPage'));
const ProfilePage = React.lazy(() => import('./pages/ProfilePage'));
const SettingsPage = React.lazy(() => import('./pages/SettingsPage'));
const ComponentShowcasePage = React.lazy(() => import('./pages/ComponentShowcasePage'));

function App() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const isMobile = useIsMobile();

  // Initialize security measures on app start
  useEffect(() => {
    initializeSecurity();
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <PreferencesProvider>
            <AppProvider>
              <Router>
              {/* Skip Navigation Links */}
              <SkipNavigation />

              <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex transition-colors duration-200">
                {/* Sidebar */}
                <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

                {/* Main content */}
                <div className="flex-1 flex flex-col lg:ml-0">
                  {/* Use MobileNavbar for better mobile experience */}
                  {isMobile ? (
                    <MobileNavbar
                      onMenuClick={() => setSidebarOpen(true)}
                      searchEnabled={true}
                    />
                  ) : (
                    <Navbar onMenuClick={() => setSidebarOpen(true)} />
                  )}

                  {/* Main content area */}
                  <main
                    className="flex-1 p-4 lg:p-8 pb-20 lg:pb-8"
                    id="main-content"
                    role="main"
                  >
                    {/* Breadcrumb navigation */}
                    <Breadcrumb className="mb-4" />

                    <ErrorBoundary>
                      <Suspense fallback={<PageLoading />}>
                        <Routes>
                          <Route path="/" element={<HomePage />} />
                          <Route path="/login" element={<LoginPage />} />
                          <Route path="/register" element={<RegisterPage />} />
                          <Route path="/dashboard" element={<DashboardPage />} />
                          <Route path="/profile" element={<ProfilePage />} />
                          <Route path="/settings" element={<SettingsPage />} />
                          <Route path="/showcase" element={<ComponentShowcasePage />} />
                        </Routes>
                      </Suspense>
                    </ErrorBoundary>
                  </main>
                </div>

                {/* Bottom Navigation for mobile */}
                {isMobile && <BottomNavigation />}

                <ToastContainer />
              </div>
            </Router>
          </AppProvider>
        </PreferencesProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
